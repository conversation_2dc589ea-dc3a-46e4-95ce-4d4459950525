<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Edit User Info')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/typography.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/katex.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/editor.css')); ?>" />

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    .editable-input span,
    .editable-input input,
    .editable-input input:focus {
        background-color: #f1f1f161;
    }
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Edit User Info')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('User Management')); ?></li>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.settings.user.index')); ?>"><?php echo e(__('All Users')); ?></a>
    </li>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.settings.user.show.profile', ['user' => $user])); ?>"><?php echo e($user->userid); ?></a>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('Edit User Info')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-12"></div>
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">Edit User Info of <b class="text-primary"><?php echo e($user->name .' ('. $user->employee->alias_name .')'); ?></b></h5>

                <div class="card-header-elements ms-auto">
                    <a href="<?php echo e(route('administration.settings.user.show.profile', ['user' => $user])); ?>" class="btn btn-sm btn-primary">
                        <span class="tf-icon ti ti-arrow-left ti-xs me-1"></span>
                        Back
                    </a>
                </div>
            </div>
            <!-- Account -->
            <div class="card-body">
                <form id="profileForm" action="<?php echo e(route('administration.settings.user.update', ['user' => $user])); ?>" method="post" enctype="multipart/form-data" autocomplete="off">
                    <?php echo csrf_field(); ?>
                    <div class="d-flex align-items-start align-items-sm-center gap-4">
                        <?php if($user->hasMedia('avatar')): ?>
                            <img src="<?php echo e($user->getFirstMediaUrl('avatar')); ?>" alt="<?php echo e($user->name); ?> Avatar" class="d-block w-px-100 h-px-100 rounded" id="uploadedAvatar">
                        <?php else: ?>
                            <img src="<?php echo e(asset('assets/img/avatars/no_image.png')); ?>" alt="<?php echo e($user->name); ?> No Avatar" class="d-block w-px-100 h-px-100 rounded" id="uploadedAvatar">
                        <?php endif; ?>
                        <div class="button-wrapper">
                            <label for="upload" class="btn btn-primary me-2 mb-3" tabindex="0">
                                <span class="d-none d-sm-block">Upload Avatar</span>
                                <i class="ti ti-upload d-block d-sm-none"></i>
                                <input type="file" name="avatar" id="upload" class="account-file-input" hidden accept="image/png, image/jpeg, image/jpg"/>
                            </label>
                            <button type="button" class="btn btn-label-secondary account-image-reset mb-3">
                                <i class="ti ti-refresh-dot d-block d-sm-none"></i>
                                <span class="d-none d-sm-block">Reset</span>
                            </button>

                            <div class="text-muted">Upload a <b class="text-dark">Square Image (1:1 ratio) in JPG, JPEG, or PNG</b> format. Maximum size: <b class="text-dark">2MB</b>.</div>
                            <?php $__errorArgs = ['avatar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="ti ti-info-circle mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <hr class="my-3" />

                    <div class="row">
                        <div class="mb-3 col-md-6">
                            <label for="role_id" class="form-label">Select Role <strong class="text-danger">*</strong></label>
                            <select name="role_id" class="select2 form-select <?php $__errorArgs = ['role_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true" required autofocus>
                                <option value="" selected disabled>Select Role</option>
                                <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($role->id); ?>" <?php if(old('role_id') == $role->id || ($user->role->id == $role->id)): echo 'selected'; endif; ?>><?php echo e($role->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['role_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="ti ti-info-circle mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-6">
                            <label class="form-label d-block" for="email">
                                <span class="float-start">
                                    Login Email <strong class="text-danger">*</strong>
                                </span>
                                <span class="float-end">
                                    <a href="javascript:void(0);" id="editEmail" class="text-primary">
                                        Edit Email
                                    </a>
                                    <a href="javascript:void(0);" id="doneEditEmail" class="text-primary d-none">
                                        Done
                                    </a>
                                </span>
                            </label>
                            <div class="input-group input-group-merge mt-4 editable-input" id="editableInput">
                                <span class="input-group-text"><i class="ti ti-mail"></i></span>
                                <input type="email" id="email" name="email" value="<?php echo e(old('email', $user->email)); ?>" placeholder="<?php echo e(__('Email')); ?>" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" readonly required/>
                            </div>
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="ti ti-info-circle mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-4">
                            <label for="first_name" class="form-label"><?php echo e(__('First Name')); ?> <strong class="text-danger">*</strong></label>
                            <input type="text" id="first_name" name="first_name" value="<?php echo e(old('first_name', $user->first_name)); ?>" placeholder="<?php echo e(__('First Name')); ?>" class="form-control <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required/>
                            <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="ti ti-info-circle mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-4">
                            <label for="last_name" class="form-label"><?php echo e(__('Surname / Family Name / Last Name')); ?> <strong class="text-danger">*</strong></label>
                            <input type="text" id="last_name" name="last_name" value="<?php echo e(old('last_name', $user->last_name)); ?>" placeholder="<?php echo e(__('Surname / Family Name / Last Name')); ?>" class="form-control <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required/>
                            <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="ti ti-info-circle mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-4">
                            <label for="alias_name" class="form-label"><?php echo e(__('Alias Name')); ?> <strong class="text-danger">*</strong></label>
                            <input type="text" id="alias_name" name="alias_name" value="<?php echo e(old('alias_name', optional($user->employee)->alias_name)); ?>" placeholder="<?php echo e(__('Alias Name')); ?>" class="form-control <?php $__errorArgs = ['alias_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required/>
                            <?php $__errorArgs = ['alias_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="ti ti-info-circle mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-2">
                            <label class="form-label">Joining Date <strong class="text-danger">*</strong></label>
                            <input type="text" name="joining_date" value="<?php echo e(old('joining_date', optional($user->employee)->joining_date)); ?>" class="form-control  date-picker" placeholder="YYYY-MM-DD" required/>
                            <?php $__errorArgs = ['joining_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-4">
                            <label for="father_name" class="form-label"><?php echo e(__('Father Name')); ?> <strong class="text-danger">*</strong></label>
                            <input type="text" id="father_name" name="father_name" value="<?php echo e(old('father_name', optional($user->employee)->father_name)); ?>" placeholder="<?php echo e(__('Father Name')); ?>" class="form-control <?php $__errorArgs = ['father_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required/>
                            <?php $__errorArgs = ['father_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-4">
                            <label for="mother_name" class="form-label"><?php echo e(__('Mother Name')); ?> <strong class="text-danger">*</strong></label>
                            <input type="text" id="mother_name" name="mother_name" value="<?php echo e(old('mother_name', optional($user->employee)->mother_name)); ?>" placeholder="<?php echo e(__('Mother Name')); ?>" class="form-control <?php $__errorArgs = ['mother_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required/>
                            <?php $__errorArgs = ['mother_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-2">
                            <label class="form-label">Birthdate <strong class="text-danger">*</strong></label>
                            <input type="text" name="birth_date" value="<?php echo e(old('birth_date', optional($user->employee)->birth_date)); ?>" class="form-control  date-picker" placeholder="YYYY-MM-DD" required/>
                            <?php $__errorArgs = ['birth_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-3">
                            <label for="personal_email" class="form-label"><?php echo e(__('Personal Email')); ?> <strong class="text-danger">*</strong></label>
                            <input type="email" id="personal_email" name="personal_email" value="<?php echo e(old('personal_email', optional($user->employee)->personal_email)); ?>" placeholder="<?php echo e(__('Personal Email')); ?>" class="form-control <?php $__errorArgs = ['personal_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required/>
                            <?php $__errorArgs = ['personal_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-3">
                            <label for="official_email" class="form-label"><?php echo e(__('Official Email')); ?></label>
                            <input type="email" id="official_email" name="official_email" value="<?php echo e(old('official_email', optional($user->employee)->official_email)); ?>" placeholder="<?php echo e(__('Official Email')); ?>" class="form-control <?php $__errorArgs = ['official_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"/>
                            <?php $__errorArgs = ['official_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-3">
                            <label for="personal_contact_no" class="form-label"><?php echo e(__('Personal Contact No.')); ?> <strong class="text-danger">*</strong></label>
                            <input type="tel" id="personal_contact_no" name="personal_contact_no" value="<?php echo e(old('personal_contact_no', optional($user->employee)->personal_contact_no)); ?>" placeholder="<?php echo e(__('Personal Contact No.')); ?>" class="form-control <?php $__errorArgs = ['personal_contact_no'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required/>
                            <?php $__errorArgs = ['personal_contact_no'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-3">
                            <label for="official_contact_no" class="form-label"><?php echo e(__('Official Contact No.')); ?></label>
                            <input type="tel" id="official_contact_no" name="official_contact_no" value="<?php echo e(old('official_contact_no', optional($user->employee)->official_contact_no)); ?>" placeholder="<?php echo e(__('Official Contact No.')); ?>" class="form-control <?php $__errorArgs = ['official_contact_no'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"/>
                            <?php $__errorArgs = ['official_contact_no'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-4">
                            <label for="religion_id" class="form-label"><?php echo e(__('Select Religion')); ?> <strong class="text-danger">*</strong></label>
                            <select name="religion_id" id="religion_id" class="form-select bootstrap-select w-100 <?php $__errorArgs = ['religion_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"  data-style="btn-default" required>
                                <option value=""><?php echo e(__('Select Religion')); ?></option>
                                <?php $__currentLoopData = $religions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $religion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($religion->id); ?>" <?php if(optional($user->religion)->id == $religion->id): echo 'selected'; endif; ?>><?php echo e($religion->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['religion_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-4">
                            <label for="gender" class="form-label"><?php echo e(__('Select Gender')); ?> <strong class="text-danger">*</strong></label>
                            <select name="gender" id="gender" class="form-select bootstrap-select w-100 <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"  data-style="btn-default" required>
                                <option value=""><?php echo e(__('Select Gender')); ?></option>
                                <option value="Male" <?php if($user->employee->gender === 'Male'): echo 'selected'; endif; ?>>Male</option>
                                <option value="Female" <?php if($user->employee->gender === 'Female'): echo 'selected'; endif; ?>>Female</option>
                                <option value="Other" <?php if($user->employee->gender === 'Other'): echo 'selected'; endif; ?>>Other</option>
                            </select>
                            <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-4">
                            <label for="blood_group" class="form-label">
                                <?php echo e(__('Blood Group')); ?>

                            </label>
                            <select name="blood_group" class="form-select select2">
                                <option value="" <?php if($user->employee->blood_group == ''): echo 'selected'; endif; ?>>Select Blood Group</option>

                                <?php $__currentLoopData = $groupedBloodGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $groupLabel => $groupOptions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <optgroup label="<?php echo e($groupLabel); ?>">
                                        <?php $__currentLoopData = $groupOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bloodOption): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($bloodOption->value); ?>" <?php if($user->employee->blood_group === $bloodOption->value): echo 'selected'; endif; ?>>
                                                <?php echo e($bloodOption->value); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </optgroup>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                <option value="Unknown" <?php if($user->employee->blood_group === 'Unknown'): echo 'selected'; endif; ?>>
                                    Don't Know (Unknown)
                                </option>
                            </select>

                            <?php $__errorArgs = ['blood_group'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted fw-semibold">Academic Information</h6>
                            <hr class="mt-0">
                        </div>
                        <div class="mb-3 col-md-6">
                            <label for="institute_id" class="form-label"><?php echo e(__('Institute')); ?></label>
                            <select name="institute_id" id="institute_id" class="form-select select2-tags <?php $__errorArgs = ['institute_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true" data-tags="true" data-placeholder="Select or type to add new institute">
                                <option value=""><?php echo e(__('Select Institute')); ?></option>
                                <?php $__currentLoopData = $institutes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $institute): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($institute->id); ?>" <?php echo e(old('institute_id', optional($user->employee)->institute_id) == $institute->id ? 'selected' : ''); ?>>
                                        <?php echo e($institute->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <small class="text-muted">You can type to add a new institute if not found in the list</small>
                            <?php $__errorArgs = ['institute_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-4">
                            <label for="education_level_id" class="form-label"><?php echo e(__('Education Level')); ?></label>
                            <select name="education_level_id" id="education_level_id" class="form-select select2-tags <?php $__errorArgs = ['education_level_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true" data-tags="true" data-placeholder="Select or type to add new education level">
                                <option value=""><?php echo e(__('Select Education Level')); ?></option>
                                <?php $__currentLoopData = $educationLevels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $level): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($level->id); ?>" <?php echo e(old('education_level_id', optional($user->employee)->education_level_id) == $level->id ? 'selected' : ''); ?>>
                                        <?php echo e($level->title); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <small class="text-muted">You can type to add a new education level if not found in the list</small>
                            <?php $__errorArgs = ['education_level_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-2">
                            <label for="passing_year" class="form-label"><?php echo e(__('Passing Year / Exp. Year')); ?></label>
                            <input type="number" id="passing_year" name="passing_year" value="<?php echo e(old('passing_year', optional($user->employee)->passing_year)); ?>" placeholder="<?php echo e(__('e.g., 2020')); ?>" min="1950" max="<?php echo e(date('Y') + 10); ?>" class="form-control <?php $__errorArgs = ['passing_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"/>
                            <?php $__errorArgs = ['passing_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="mb-3 col-md-12">
                            <label class="form-label">User's Note</label>
                            <div name="note" id="full-editor"><?php echo old('note', optional($user->employee)->note); ?></div>
                            <textarea class="d-none" name="note" id="note-input"><?php echo e(old('note', optional($user->employee)->note)); ?></textarea>
                            <?php $__errorArgs = ['note'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    <div class="mt-2 float-end">
                        <button type="reset" onclick="return confirm('Sure Want To Reset?');" class="btn btn-outline-danger me-2">Reset Form</button>
                        <button type="submit" class="btn btn-primary">Update User Info</button>
                    </div>
                </form>
            </div>
            <!-- /Account -->
        </div>
    </div>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/form-layouts.js')); ?>"></script>


    <!-- Vendors JS -->
    <script src="<?php echo e(asset('assets/vendor/libs/quill/katex.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/quill/quill.js')); ?>"></script>

    
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function() {
            $('.bootstrap-select').each(function() {
                if (!$(this).data('bs.select')) { // Check if it's already initialized
                    $(this).selectpicker();
                }
            });
        });
    </script>
    <script>
        $(document).ready(function () {
            let accountUserImage = $("#uploadedAvatar");
            const fileInput = $(".account-file-input");
            const resetFileInput = $(".account-image-reset");

            if (accountUserImage.length > 0) {
                const resetImage = accountUserImage.attr("src");
                fileInput.on("change", function () {
                    if (this.files[0]) {
                        accountUserImage.attr("src", window.URL.createObjectURL(this.files[0]));
                    }
                });
                resetFileInput.on("click", function () {
                    fileInput.val("");
                    accountUserImage.attr("src", resetImage);
                });
            }
        });
    </script>

    <script>
        $(document).ready(function () {
            $("#editEmail").on("click", function () {
                $(this).addClass("d-none");
                $("#doneEditEmail").removeClass("d-none");
                $("#editableInput").removeClass("editable-input");

                $("#email").prop("readonly", false);
            });

            $("#doneEditEmail").on("click", function () {
                $(this).addClass("d-none");
                $("#editEmail").removeClass("d-none");
                $("#editableInput").addClass("editable-input");

                $("#email").prop("readonly", true);
            });
        });
    </script>

    <script>
        // Custom Script Here
        $(document).ready(function() {
            $('.date-picker').datepicker({
                format: 'yyyy-mm-dd',
                todayHighlight: true,
                autoclose: true,
                orientation: 'auto right'
            });
        });
    </script>

    <script>
        // Select2 with tagging functionality for academic fields
        $(document).ready(function() {
            // Initialize Select2 with tagging for institutes
            $('#institute_id').select2({
                tags: true,
                tokenSeparators: [], // Remove space and comma separators
                createTag: function (params) {
                    var term = $.trim(params.term);
                    if (term === '') {
                        return null;
                    }
                    return {
                        id: 'new:' + term,
                        text: term + ' (New Institute)',
                        newTag: true
                    };
                },
                templateResult: function (data) {
                    var $result = $('<span></span>');
                    $result.text(data.text);
                    if (data.newTag) {
                        $result.append(' <em>(will be created)</em>');
                    }
                    return $result;
                },
                insertTag: function (data, tag) {
                    // Only insert if user explicitly selects the tag
                    data.push(tag);
                }
            });

            // Initialize Select2 with tagging for education levels
            $('#education_level_id').select2({
                tags: true,
                tokenSeparators: [], // Remove space and comma separators
                createTag: function (params) {
                    var term = $.trim(params.term);
                    if (term === '') {
                        return null;
                    }
                    return {
                        id: 'new:' + term,
                        text: term + ' (New Education Level)',
                        newTag: true
                    };
                },
                templateResult: function (data) {
                    var $result = $('<span></span>');
                    $result.text(data.text);
                    if (data.newTag) {
                        $result.append(' <em>(will be created)</em>');
                    }
                    return $result;
                },
                insertTag: function (data, tag) {
                    // Only insert if user explicitly selects the tag
                    data.push(tag);
                }
            });
        });
    </script>


    <script>
        $(document).ready(function () {
            var fullToolbar = [
                [{ font: [] }, { size: [] }],
                ["bold", "italic", "underline", "strike"],
                [{ color: [] }, { background: [] }],
                ["link"],
                [{ header: "1" }, { header: "2" }, "blockquote", "code-block"],
                [{ list: "ordered" }, { list: "bullet" }, { indent: "-1" }, { indent: "+1" }],
            ];

            var fullEditor = new Quill("#full-editor", {
                bounds: "#full-editor",
                placeholder: "Ex: Mr. John Doe got promoted as Manager",
                modules: {
                    formula: true,
                    toolbar: fullToolbar,
                },
                theme: "snow",
            });

            // Set the editor content to the old note if validation fails
            <?php if(old('note')): ?>
                fullEditor.root.innerHTML = <?php echo json_encode(old('note')); ?>;
            <?php endif; ?>

            $('#profileForm').on('submit', function() {
                $('#note-input').val(fullEditor.root.innerHTML);
            });
        });
    </script>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/settings/user/edit.blade.php ENDPATH**/ ?>