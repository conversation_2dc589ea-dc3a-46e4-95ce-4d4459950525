<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Work Update Rating - Both Pages Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        .ti {
            width: 1em;
            height: 1em;
            display: inline-block;
        }
        .ti-star-filled::before {
            content: "★";
        }
        .ti-star::before {
            content: "☆";
        }
        .ti-check::before {
            content: "✓";
        }
        .ti-loader-2::before {
            content: "⟳";
        }
        .ti-spin {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .page-section {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h2 class="text-center mb-4">Daily Work Update Rating System - Both Pages</h2>
        <p class="text-center text-muted">Testing the AJAX rating functionality implemented on both pages</p>
        
        <!-- My Daily Work Updates Page Simulation -->
        <div class="page-section p-4">
            <h4 class="text-primary mb-3">
                <i class="ti ti-user me-2"></i>My Daily Work Updates Page
                <small class="text-muted">(resources/views/administration/daily_work_update/my.blade.php)</small>
            </h4>
            
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">Sample Work Update - Employee View</h6>
                    <p class="card-text">Work update content for employee's own submissions...</p>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="badge bg-danger rating-display">Not Reviewed</span>
                        </div>
                        
                        <div class="dropdown">
                            <button class="btn btn-sm btn-dark dropdown-toggle" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="text-white ti ti-check"></i>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                <li><a class="dropdown-item rating-update" href="#" data-rating="1"><i class="ti ti-star me-2"></i>1</a></li>
                                <li><a class="dropdown-item rating-update" href="#" data-rating="2"><i class="ti ti-star me-2"></i>2</a></li>
                                <li><a class="dropdown-item rating-update" href="#" data-rating="3"><i class="ti ti-star me-2"></i>3</a></li>
                                <li><a class="dropdown-item rating-update" href="#" data-rating="4"><i class="ti ti-star me-2"></i>4</a></li>
                                <li><a class="dropdown-item rating-update" href="#" data-rating="5"><i class="ti ti-star me-2"></i>5</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- All Daily Work Updates Page Simulation -->
        <div class="page-section p-4">
            <h4 class="text-success mb-3">
                <i class="ti ti-list me-2"></i>All Daily Work Updates Page
                <small class="text-muted">(resources/views/administration/daily_work_update/index.blade.php)</small>
            </h4>
            
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">Sample Work Update - Admin View</h6>
                    <p class="card-text">Work update content for admin/manager review...</p>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="badge bg-danger rating-display">Not Reviewed</span>
                        </div>
                        
                        <div class="dropdown">
                            <button class="btn btn-sm btn-dark dropdown-toggle" type="button" id="dropdownMenuButton2" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="text-white ti ti-check"></i>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton2">
                                <li><a class="dropdown-item rating-update" href="#" data-rating="1"><i class="ti ti-star me-2"></i>1</a></li>
                                <li><a class="dropdown-item rating-update" href="#" data-rating="2"><i class="ti ti-star me-2"></i>2</a></li>
                                <li><a class="dropdown-item rating-update" href="#" data-rating="3"><i class="ti ti-star me-2"></i>3</a></li>
                                <li><a class="dropdown-item rating-update" href="#" data-rating="4"><i class="ti ti-star me-2"></i>4</a></li>
                                <li><a class="dropdown-item rating-update" href="#" data-rating="5"><i class="ti ti-star me-2"></i>5</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Features Summary -->
        <div class="row">
            <div class="col-md-6">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">✅ Features Implemented</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li>✅ AJAX Rating System</li>
                            <li>✅ Toast Notifications</li>
                            <li>✅ Button State Changes</li>
                            <li>✅ Loading Indicators</li>
                            <li>✅ Error Handling</li>
                            <li>✅ Badge Color Updates</li>
                            <li>✅ Console Debugging</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">🎨 Button States</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><span class="badge bg-dark me-2">btn-dark</span> Unrated</li>
                            <li><span class="badge bg-outline-dark me-2">btn-outline-dark</span> Rated</li>
                            <li><span class="badge bg-secondary me-2">disabled</span> Loading</li>
                        </ul>
                        <hr>
                        <small class="text-muted">
                            <strong>Rating Colors:</strong><br>
                            1=Danger, 2=Warning, 3=Dark, 4=Primary, 5=Success
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-success mt-4">
            <h6><i class="ti ti-check me-2"></i>Implementation Complete!</h6>
            <p class="mb-0">
                Both <code>my.blade.php</code> and <code>index.blade.php</code> now have identical AJAX rating functionality 
                with toast notifications and proper button styling.
            </p>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            $('.rating-update').click(function(e) {
                e.preventDefault();

                var $this = $(this);
                var rating = $this.data('rating');
                var $dropdown = $this.closest('.dropdown');
                var $button = $dropdown.find('.dropdown-toggle');
                
                // Show loading state
                var originalButtonHtml = $button.html();
                $button.html('<i class="text-white ti ti-loader-2 ti-spin"></i>').prop('disabled', true);
                
                // Close dropdown
                $dropdown.find('.dropdown-menu').removeClass('show');
                $button.removeClass('show').attr('aria-expanded', 'false');

                // Simulate AJAX request with timeout
                setTimeout(function() {
                    // Update button to show rating with new styling
                    $button.html('<i class="ti ti-star-filled me-1"></i>' + rating)
                           .removeClass('btn-dark btn-primary')
                           .addClass('btn-outline-dark')
                           .prop('disabled', false);
                    
                    // Update the rating display
                    var $ratingCell = $this.closest('.card').find('.rating-display');
                    if ($ratingCell.length) {
                        // Determine badge color based on rating
                        var badgeColor = 'success';
                        switch(rating) {
                            case '1': badgeColor = 'danger'; break;
                            case '2': badgeColor = 'warning'; break;
                            case '3': badgeColor = 'dark'; break;
                            case '4': badgeColor = 'primary'; break;
                            default: badgeColor = 'success'; break;
                        }
                        $ratingCell.removeClass('bg-danger bg-warning bg-dark bg-primary bg-success')
                                  .addClass('bg-' + badgeColor)
                                  .text(rating + ' out of 5');
                    }
                    
                    // Show SweetAlert2 toast notification
                    Swal.fire({
                        icon: 'success',
                        title: 'Rating Updated!',
                        text: 'Daily Work Update has been rated ' + rating + ' out of 5 successfully.',
                        timer: 3000,
                        showConfirmButton: false,
                        toast: true,
                        position: 'top-end',
                        timerProgressBar: true
                    });
                }, 1000); // Simulate 1 second delay
            });
        });
    </script>
</body>
</html>
