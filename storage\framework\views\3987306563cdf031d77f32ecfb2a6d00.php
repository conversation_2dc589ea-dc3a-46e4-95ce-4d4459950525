<?php $__env->startSection('css_links_user_show'); ?>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/css/lightbox.min.css" integrity="sha512-ZKX+BvQihRJPA8CROKBhDNvoc2aDMOdAlcm7TUQY+35XYtrd3yh95QOOhsPDQY9QnKE0Wqag9y38OIgEvb88cA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('profile_content'); ?>

<!-- User Files -->
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0"><?php echo e(__('All Files')); ?></h5>

                <div class="card-header-elements ms-auto">
                    <a href="javascript:void(0);" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addUserFileModal" title="Add File">
                        <span class="tf-icon ti ti-user-plus ti-xs me-1"></span>
                        Add File
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if($user->files->count() > 0): ?>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Note</th>
                                        <th>Size</th>
                                        <th>Uploaded</th>
                                        <th class="text-center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $user->files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <?php if(in_array($file->mime_type, ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'])): ?>
                                                    <div class="task-image-container">
                                                        <a href="<?php echo e(file_media_download($file)); ?>" data-lightbox="task-images" data-title="<?php echo e($file->original_name); ?>">
                                                            <img src="<?php echo e(file_media_download($file)); ?>" alt="<?php echo e($file->original_name); ?>" class="img-fluid img-thumbnail" style="width: 150px; height: 100px; object-fit: cover;">
                                                        </a>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="file-thumbnail-container">
                                                        <i class="ti ti-file-download fs-2 mb-2 text-primary"></i>
                                                        <span class="file-name text-center small fw-medium" title="<?php echo e($file->original_name); ?>">
                                                            <?php echo e(show_content($file->original_name, 15)); ?>

                                                        </span>
                                                        <small class="text-muted"><?php echo e(strtoupper(pathinfo($file->original_name, PATHINFO_EXTENSION))); ?></small>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $file->note; ?></td>
                                            <td><?php echo e(get_file_media_size($file)); ?></td>
                                            <td><?php echo e(date_time_ago($file->created_at)); ?></td>
                                            <td class="text-center">
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['User Everything', 'User Delete'])): ?>
                                                    <a href="<?php echo e(file_media_destroy($file)); ?>" class="btn btn-icon btn-label-danger btn-sm waves-effect confirm-danger" title="Delete <?php echo e($file->original_name); ?>">
                                                        <i class="ti ti-trash"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <a href="<?php echo e(file_media_download($file)); ?>" target="_blank" class="btn btn-icon btn-primary btn-sm waves-effect" title="Download <?php echo e($file->original_name); ?>">
                                                    <i class="ti ti-download"></i>
                                                </a>
                                                <a href="<?php echo e(get_file_media_url($file)); ?>" target="_blank" class="btn btn-icon btn-dark btn-sm waves-effect" title="View <?php echo e($file->original_name); ?>">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<!--/ User Files -->



<?php echo $__env->make('administration.settings.user.includes.modals.add_file', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links_user_show'); ?>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/js/lightbox.min.js" integrity="sha512-Ixzuzfxv1EqafeQlTCufWfaC6ful6WFqIz4G+dWvK0beHw0NVJwvCKSgafpy5gwNqKmgUfIBraVwkKI+Cz0SEQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('administration.settings.user.show', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/settings/user/includes/user_files.blade.php ENDPATH**/ ?>