{"__meta": {"id": "01JZT2NCJXEGB7ZBF8S636NRQ2", "datetime": "2025-07-10 17:48:48", "utime": **********.351263, "method": "GET", "uri": "/_ignition/health-check", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.486991, "end": **********.351301, "duration": 4.864310026168823, "duration_str": "4.86s", "measures": [{"label": "Booting", "start": **********.486991, "relative_start": 0, "end": **********.386647, "relative_end": **********.386647, "duration": 2.***************, "duration_str": "2.9s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.386674, "relative_start": 2.****************, "end": **********.351305, "relative_end": 4.0531158447265625e-06, "duration": 1.****************, "duration_str": "1.96s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.459504, "relative_start": 2.**************, "end": **********.472112, "relative_end": **********.472112, "duration": 0.012608051300048828, "duration_str": "12.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.335336, "relative_start": 4.***************, "end": **********.344304, "relative_end": **********.344304, "duration": 0.008968114852905273, "duration_str": "8.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.344349, "relative_start": 4.***************, "end": **********.344396, "relative_end": **********.344396, "duration": 4.7206878662109375e-05, "duration_str": "47μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET _ignition/health-check", "middleware": "Spatie\\LaravelIgnition\\Http\\Middleware\\RunnableSolutionsEnabled", "uses": "Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController@__invoke", "controller": "Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController", "as": "ignition.healthCheck", "prefix": "_ignition"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"data": {"status": "200 OK", "full_url": "https://blueorange.test/_ignition/health-check", "action_name": "ignition.healthCheck", "controller_action": "Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController", "uri": "GET _ignition/health-check", "controller": "Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController", "prefix": "_ignition", "middleware": "Spatie\\LaravelIgnition\\Http\\Middleware\\RunnableSolutionsEnabled", "duration": "4.86s", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-669770870 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-669770870\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-890893614 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-890893614\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">https://blueorange.test/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1072 characters\">laravel_session=eyJpdiI6ImEvVXBHZ3REcTZOVHpNNWpFRjZYanc9PSIsInZhbHVlIjoid1ZSNnVYRnFkRnk5R3NSTGpRSGVpUmdJNndaMW1lWUtFNmtvUzNqYVg5Tm01clNWRVpmREt5NDVoemQwVmhpWnFZYlNJWEpHRGV5MVVQZXVwaktWMnlXQUpCV3dxS2l3SVlRZVJYV3lIYkYzcktxUkZLT3JGS3JlR2lCSUdHM2EiLCJtYWMiOiI2M2ZhMTQyNGFkYTMxMWU1OTkwYWJhYjRmOTVjNTljMjJjYTQ4NTEwOGNiZTI1MmVjODc4ZDQ3OGZhMzRhMThhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik1UUHUzVzA4aC9FOXNtOWprR0pwTWc9PSIsInZhbHVlIjoiMWdiV1BiMTJINDh5UUxYK201RTFIeFYwUVRwMEhrRkh4QjhkM1dkRDUzaWdDVVo5T1Izc1VHaUZiR2s5UFlsd3AyRlpoSGExZVRKS2owaGMyODZwUUIxRGRXY3F4NG1jRkNRSjhEMW1YUGZVak9tRGpxQVlld1k5b256U1ZsWkkiLCJtYWMiOiJlZTRiNWM0Y2RjZTI1ZjljMTRmYjkwYmQ5OThjN2EwYWE2NTQwYTdjMGNjMjIzOWU1ZjVlZDYwYjE5ZmI4NjNiIiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6IkttMU1ra3BGaFo3Rk5JMGs3L3ZTSVE9PSIsInZhbHVlIjoiYms3SW05ajdZM25PVFVNeWw2bDJyVG0vZ09DNk5EbWlYdWxiL2pIdUxqTEI0bDNKcmpQUmtPMW5Gc2cxeWd0YkRRVWQreTBESmhVTlBFSFVGTUhFWTZ2VzZ0YXZDYkx0QUVycWZxa2NkNGhreHhMcW5QNVBiYmh3OHpuMGdUeDMiLCJtYWMiOiJlOGEyNzE1MjJiYWYyMDI2YzM3MGFhMDlkNDA5NGI3ZDM5ZWQxOGJmYzFmOTM2ZDA1OTM1Nzg4NWVhN2NmMzlmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImEvVXBHZ3REcTZOVHpNNWpFRjZYanc9PSIsInZhbHVlIjoid1ZSNnVYRnFkRnk5R3NSTGpRSGVpUmdJNndaMW1lWUtFNmtvUzNqYVg5Tm01clNWRVpmREt5NDVoemQwVmhpWnFZYlNJWEpHRGV5MVVQZXVwaktWMnlXQUpCV3dxS2l3SVlRZVJYV3lIYkYzcktxUkZLT3JGS3JlR2lCSUdHM2EiLCJtYWMiOiI2M2ZhMTQyNGFkYTMxMWU1OTkwYWJhYjRmOTVjNTljMjJjYTQ4NTEwOGNiZTI1MmVjODc4ZDQ3OGZhMzRhMThhIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik1UUHUzVzA4aC9FOXNtOWprR0pwTWc9PSIsInZhbHVlIjoiMWdiV1BiMTJINDh5UUxYK201RTFIeFYwUVRwMEhrRkh4QjhkM1dkRDUzaWdDVVo5T1Izc1VHaUZiR2s5UFlsd3AyRlpoSGExZVRKS2owaGMyODZwUUIxRGRXY3F4NG1jRkNRSjhEMW1YUGZVak9tRGpxQVlld1k5b256U1ZsWkkiLCJtYWMiOiJlZTRiNWM0Y2RjZTI1ZjljMTRmYjkwYmQ5OThjN2EwYWE2NTQwYTdjMGNjMjIzOWU1ZjVlZDYwYjE5ZmI4NjNiIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkttMU1ra3BGaFo3Rk5JMGs3L3ZTSVE9PSIsInZhbHVlIjoiYms3SW05ajdZM25PVFVNeWw2bDJyVG0vZ09DNk5EbWlYdWxiL2pIdUxqTEI0bDNKcmpQUmtPMW5Gc2cxeWd0YkRRVWQreTBESmhVTlBFSFVGTUhFWTZ2VzZ0YXZDYkx0QUVycWZxa2NkNGhreHhMcW5QNVBiYmh3OHpuMGdUeDMiLCJtYWMiOiJlOGEyNzE1MjJiYWYyMDI2YzM3MGFhMDlkNDA5NGI3ZDM5ZWQxOGJmYzFmOTM2ZDA1OTM1Nzg4NWVhN2NmMzlmIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 11:48:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-641756510 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-641756510\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://blueorange.test/_ignition/health-check", "action_name": "ignition.healthCheck", "controller_action": "Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController"}, "badge": null}}