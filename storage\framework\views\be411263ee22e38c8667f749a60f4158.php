<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('My or My Employees Daily Work Update')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- DataTables css -->
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/datatable.css')); ?>" rel="stylesheet" type="text/css" />

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('My or My Employees Daily Work Update')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Daily Work Update')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('My or My Employees Daily Work Update')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<?php if(auth()->user()->tl_employees_daily_work_updates->count() > 0): ?>
    <div class="row justify-content-center">
        <div class="col-md-8">
            <form action="<?php echo e(route('administration.daily_work_update.my')); ?>" method="get" autocomplete="off">
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="mb-3 col-md-7">
                                <label for="user_id" class="form-label">Select Employee</label>
                                <select name="user_id" id="user_id" class="select2 form-select <?php $__errorArgs = ['user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true">
                                    <option value="" <?php echo e(is_null(request()->user_id) ? 'selected' : ''); ?>>Select Employee</option>
                                    <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <optgroup label="<?php echo e($role->name); ?>">
                                            <?php $__currentLoopData = $role->users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($user->id); ?>" <?php echo e($user->id == request()->user_id ? 'selected' : ''); ?>>
                                                    <?php echo e(get_employee_name($user)); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </optgroup>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-3 col-md-5">
                                <label class="form-label">Work Updates Of</label>
                                <input type="text" name="created_month_year" value="<?php echo e(request()->created_month_year ?? old('created_month_year')); ?>" class="form-control month-year-picker" placeholder="MM yyyy" tabindex="-1"/>
                                <?php $__errorArgs = ['created_month_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="col-md-12 text-end">
                            <?php if(request()->user_id || request()->created_month_year): ?>
                                <a href="<?php echo e(route('administration.daily_work_update.my')); ?>" class="btn btn-danger confirm-warning">
                                    <span class="tf-icon ti ti-refresh ti-xs me-1"></span>
                                    Reset Filters
                                </a>
                            <?php endif; ?>
                            <button type="submit" name="filter_work_updates" value="true" class="btn btn-primary">
                                <span class="tf-icon ti ti-filter ti-xs me-1"></span>
                                Filter Work Updates
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">
                    <span>My or My Employees Work Updates</span>
                    <?php if(request()->created_month_year): ?>
                        <sup>(<b>Month: </b> <?php echo e(request()->created_month_year); ?>)</sup>
                    <?php endif; ?>
                </h5>

                <div class="card-header-elements ms-auto">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Daily Work Update Create')): ?>
                        <a href="<?php echo e(route('administration.daily_work_update.create')); ?>" class="btn btn-sm btn-primary">
                            <span class="tf-icon ti ti-plus ti-xs me-1"></span>
                            Submit Work Update
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive-md table-responsive-sm w-100">
                    <table class="table data-table table-bordered">
                        <thead>
                            <tr>
                                <th>Sl.</th>
                                <th>Date</th>
                                <th>Team Leader</th>
                                <th>Submitted At</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $dailyWorkUpdates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $dailyUpdate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="<?php if(is_null($dailyUpdate->rating)): ?> bg-label-danger <?php endif; ?>">
                                    <th>#<?php echo e(serial($dailyWorkUpdates, $key)); ?></th>
                                    <td>
                                        <b><?php echo e(show_date($dailyUpdate->date)); ?></b>
                                        <br>
                                        <?php if(!is_null($dailyUpdate->rating)): ?>
                                            <?php
                                                switch ($dailyUpdate->rating) {
                                                    case '1':
                                                        $color = 'danger';
                                                        break;
                                                    case '2':
                                                        $color = 'warning';
                                                        break;
                                                    case '3':
                                                        $color = 'dark';
                                                        break;
                                                    case '4':
                                                        $color = 'primary';
                                                        break;
                                                    default:
                                                        $color = 'success';
                                                        break;
                                                }
                                            ?>
                                            <small class="badge bg-<?php echo e($color); ?> rating-display">
                                                <?php echo e($dailyUpdate->rating); ?> out of 5
                                            </small>
                                        <?php else: ?>
                                            <small class="badge bg-danger rating-display">
                                                <?php echo e(__('Not Reviewed')); ?>

                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex justify-content-start align-items-center user-name">
                                            <div class="avatar-wrapper">
                                                <div class="avatar me-2">
                                                    <a href="<?php echo e(route('administration.settings.user.show.profile', ['user' => $dailyUpdate->team_leader])); ?>">
                                                        <?php if($dailyUpdate->team_leader->hasMedia('avatar')): ?>
                                                            <img src="<?php echo e($dailyUpdate->team_leader->getFirstMediaUrl('avatar', 'thumb')); ?>" alt="<?php echo e($dailyUpdate->team_leader->name); ?> Avatar" class="rounded-circle">
                                                        <?php else: ?>
                                                            <span class="avatar-initial rounded-circle bg-label-hover-dark text-bold">
                                                                <?php echo e(profile_name_pic($dailyUpdate->team_leader)); ?>

                                                            </span>
                                                        <?php endif; ?>
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="d-flex flex-column">
                                                <a href="<?php echo e(route('administration.settings.user.show.profile', ['user' => $dailyUpdate->team_leader])); ?>" target="_blank" class="emp_name text-truncate"><?php echo e($dailyUpdate->team_leader->alias_name); ?></a>
                                                <small class="emp_post text-truncate text-muted"><?php echo e($dailyUpdate->team_leader->role->name); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <small class="text-bold"><?php echo e(show_date($dailyUpdate->created_at)); ?></small>
                                        <br>
                                        <span>
                                            at
                                            <small class="text-bold"><?php echo e(show_time($dailyUpdate->created_at)); ?></small>
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Daily Work Update Delete')): ?>
                                            <a href="<?php echo e(route('administration.daily_work_update.destroy', ['daily_work_update' => $dailyUpdate])); ?>" class="btn btn-sm btn-icon btn-danger confirm-danger" data-bs-toggle="tooltip" title="Delete Daily Work Update?">
                                                <i class="text-white ti ti-trash"></i>
                                            </a>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Daily Work Update Read')): ?>
                                            <a href="<?php echo e(route('administration.daily_work_update.show', ['daily_work_update' => $dailyUpdate])); ?>" target="_blank" class="btn btn-sm btn-icon btn-primary" data-bs-toggle="tooltip" title="Show Details">
                                                <i class="text-white ti ti-info-hexagon"></i>
                                            </a>
                                        <?php endif; ?>

                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Daily Work Update Everything', 'Daily Work Update Update'])): ?>
                                            <div class="dropdown mt-1">
                                                <?php if(!is_null($dailyUpdate->rating)): ?>
                                                    <button class="btn btn-sm btn-outline-dark dropdown-toggle" type="button" id="dropdownMenuButton<?php echo e($dailyUpdate->id); ?>" data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="ti ti-star-filled me-1"></i><?php echo e($dailyUpdate->rating); ?>

                                                    </button>
                                                <?php else: ?>
                                                    <button class="btn btn-sm btn-dark dropdown-toggle" type="button" id="dropdownMenuButton<?php echo e($dailyUpdate->id); ?>" data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="text-white ti ti-check"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton<?php echo e($dailyUpdate->id); ?>">
                                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                                        <li>
                                                            <a class="dropdown-item rating-update" href="#"
                                                            data-url="<?php echo e(route('administration.daily_work_update.update', ['daily_work_update' => $dailyUpdate])); ?>"
                                                            data-rating="<?php echo e($i); ?>">
                                                                <i class="ti ti-star me-2"></i><?php echo e($i); ?>

                                                            </a>
                                                        </li>
                                                    <?php endfor; ?>
                                                </ul>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    <!-- Datatable js -->
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/datatable.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/js/form-layouts.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-datepicker/bootstrap-datepicker.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        // Custom Script Here
        $(document).ready(function() {
            $('.month-year-picker').datepicker({
                format: 'MM yyyy',         // Display format to show full month name and year
                minViewMode: 'months',     // Only allow month selection
                todayHighlight: true,
                autoclose: true,
                orientation: 'auto right'
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $('.rating-update').click(function(e) {
                e.preventDefault();

                var $this = $(this);
                var url = $this.data('url');
                var rating = $this.data('rating');
                var $dropdown = $this.closest('.dropdown');
                var $button = $dropdown.find('.dropdown-toggle');

                // Debug logging
                console.log('Rating Update Request:', {
                    url: url,
                    rating: rating
                });

                // Show loading state
                var originalButtonHtml = $button.html();
                $button.html('<i class="text-white ti ti-loader-2 ti-spin"></i>').prop('disabled', true);

                // Close dropdown
                $dropdown.find('.dropdown-menu').removeClass('show');
                $button.removeClass('show').attr('aria-expanded', 'false');

                $.ajax({
                    url: url,
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>',
                        rating: rating
                    },
                    success: function(response) {
                        if (response.success) {
                            // Update button to show rating with new styling
                            $button.html('<i class="ti ti-star-filled me-1"></i>' + rating)
                                   .removeClass('btn-dark btn-primary')
                                   .addClass('btn-outline-dark')
                                   .prop('disabled', false);

                            // Show toast success message
                            <?php if(function_exists('toast')): ?>
                                // Use Laravel SweetAlert toast
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Rating Updated!',
                                    text: response.message || 'Daily Work Update has been rated successfully.',
                                    timer: 3000,
                                    showConfirmButton: false,
                                    toast: true,
                                    position: 'top-end',
                                    timerProgressBar: true
                                });
                            <?php else: ?>
                                // Fallback to toastr if available
                                if (typeof toastr !== 'undefined') {
                                    toastr.success(response.message || 'Rating updated successfully');
                                } else {
                                    alert(response.message || 'Rating updated successfully');
                                }
                            <?php endif; ?>

                            // Update the rating display in the row if exists
                            var $ratingCell = $this.closest('tr').find('.rating-display');
                            if ($ratingCell.length) {
                                // Determine badge color based on rating
                                var badgeColor = 'success';
                                switch(rating) {
                                    case '1': badgeColor = 'danger'; break;
                                    case '2': badgeColor = 'warning'; break;
                                    case '3': badgeColor = 'dark'; break;
                                    case '4': badgeColor = 'primary'; break;
                                    default: badgeColor = 'success'; break;
                                }
                                $ratingCell.removeClass('bg-danger bg-warning bg-dark bg-primary bg-success')
                                          .addClass('bg-' + badgeColor)
                                          .text(rating + ' out of 5');
                            }

                            // Remove the danger background from the row if it was unrated
                            $this.closest('tr').removeClass('bg-label-danger');
                        } else {
                            throw new Error(response.message || 'Unknown error occurred');
                        }
                    },
                    error: function(xhr) {
                        // Restore original button state
                        $button.html(originalButtonHtml).prop('disabled', false);

                        // Log error details for debugging
                        console.log('AJAX Error Details:', {
                            status: xhr.status,
                            statusText: xhr.statusText,
                            responseText: xhr.responseText,
                            url: url,
                            rating: rating
                        });

                        var errorMessage = 'Failed to update rating';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        } else if (xhr.status === 404) {
                            errorMessage = 'Daily Work Update not found. Please refresh the page and try again.';
                        } else if (xhr.status === 422) {
                            errorMessage = 'Validation error occurred';
                        } else if (xhr.status === 403) {
                            errorMessage = 'You do not have permission to perform this action';
                        } else if (xhr.status === 500) {
                            errorMessage = 'Server error occurred. Please try again.';
                        }

                        // Show toast error message
                        <?php if(function_exists('toast')): ?>
                            // Use Laravel SweetAlert toast
                            Swal.fire({
                                icon: 'error',
                                title: 'Rating Failed!',
                                text: errorMessage,
                                timer: 4000,
                                showConfirmButton: false,
                                toast: true,
                                position: 'top-end',
                                timerProgressBar: true
                            });
                        <?php else: ?>
                            // Fallback to toastr if available
                            if (typeof toastr !== 'undefined') {
                                toastr.error(errorMessage);
                            } else {
                                alert('Error: ' + errorMessage);
                            }
                        <?php endif; ?>
                    }
                });
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/daily_work_update/my.blade.php ENDPATH**/ ?>