1752148838O:55:"Illuminate\Notifications\DatabaseNotificationCollection":2:{s:8:" * items";a:3:{i:0;O:45:"Illuminate\Notifications\DatabaseNotification":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"notifications";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:6:"string";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";s:36:"47acea00-37d9-4b11-b71d-ef65d47e17dc";s:4:"type";s:76:"App\Notifications\Administration\Tickets\ItTicket\ItTicketCreateNotification";s:15:"notifiable_type";s:15:"App\Models\User";s:13:"notifiable_id";i:1;s:4:"data";s:183:"{"url":"https:\/\/app.staff-india.com\/ticket\/it_ticket\/show\/w0m8nb5BVy5WPvja","icon":"ticket","title":"New IT Ticket Arrived","message":"A New IT Ticket Has Been Arised By Harry"}";s:7:"read_at";N;s:10:"created_at";s:19:"2025-05-08 04:38:28";s:10:"updated_at";s:19:"2025-05-08 04:38:28";}s:11:" * original";a:8:{s:2:"id";s:36:"47acea00-37d9-4b11-b71d-ef65d47e17dc";s:4:"type";s:76:"App\Notifications\Administration\Tickets\ItTicket\ItTicketCreateNotification";s:15:"notifiable_type";s:15:"App\Models\User";s:13:"notifiable_id";i:1;s:4:"data";s:183:"{"url":"https:\/\/app.staff-india.com\/ticket\/it_ticket\/show\/w0m8nb5BVy5WPvja","icon":"ticket","title":"New IT Ticket Arrived","message":"A New IT Ticket Has Been Arised By Harry"}";s:7:"read_at";N;s:10:"created_at";s:19:"2025-05-08 04:38:28";s:10:"updated_at";s:19:"2025-05-08 04:38:28";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:4:"data";s:5:"array";s:7:"read_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:1;O:45:"Illuminate\Notifications\DatabaseNotification":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"notifications";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:6:"string";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";s:36:"b99504ee-9f22-4926-b5ed-7ff7b6d3ff49";s:4:"type";s:76:"App\Notifications\Administration\Tickets\ItTicket\ItTicketCreateNotification";s:15:"notifiable_type";s:15:"App\Models\User";s:13:"notifiable_id";i:1;s:4:"data";s:183:"{"url":"https:\/\/app.staff-india.com\/ticket\/it_ticket\/show\/mbZODE7rjj7dAMpK","icon":"ticket","title":"New IT Ticket Arrived","message":"A New IT Ticket Has Been Arised By Aiden"}";s:7:"read_at";N;s:10:"created_at";s:19:"2025-05-08 04:22:21";s:10:"updated_at";s:19:"2025-05-08 04:22:21";}s:11:" * original";a:8:{s:2:"id";s:36:"b99504ee-9f22-4926-b5ed-7ff7b6d3ff49";s:4:"type";s:76:"App\Notifications\Administration\Tickets\ItTicket\ItTicketCreateNotification";s:15:"notifiable_type";s:15:"App\Models\User";s:13:"notifiable_id";i:1;s:4:"data";s:183:"{"url":"https:\/\/app.staff-india.com\/ticket\/it_ticket\/show\/mbZODE7rjj7dAMpK","icon":"ticket","title":"New IT Ticket Arrived","message":"A New IT Ticket Has Been Arised By Aiden"}";s:7:"read_at";N;s:10:"created_at";s:19:"2025-05-08 04:22:21";s:10:"updated_at";s:19:"2025-05-08 04:22:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:4:"data";s:5:"array";s:7:"read_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:2;O:45:"Illuminate\Notifications\DatabaseNotification":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"notifications";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:6:"string";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";s:36:"2065282c-62e8-428c-ae1f-05f91781ddad";s:4:"type";s:76:"App\Notifications\Administration\Tickets\ItTicket\ItTicketCreateNotification";s:15:"notifiable_type";s:15:"App\Models\User";s:13:"notifiable_id";i:1;s:4:"data";s:181:"{"url":"https:\/\/app.staff-india.com\/ticket\/it_ticket\/show\/mW4OMlxv805P2L9j","icon":"ticket","title":"New IT Ticket Arrived","message":"A New IT Ticket Has Been Arised By Max"}";s:7:"read_at";N;s:10:"created_at";s:19:"2025-05-08 04:11:55";s:10:"updated_at";s:19:"2025-05-08 04:11:55";}s:11:" * original";a:8:{s:2:"id";s:36:"2065282c-62e8-428c-ae1f-05f91781ddad";s:4:"type";s:76:"App\Notifications\Administration\Tickets\ItTicket\ItTicketCreateNotification";s:15:"notifiable_type";s:15:"App\Models\User";s:13:"notifiable_id";i:1;s:4:"data";s:181:"{"url":"https:\/\/app.staff-india.com\/ticket\/it_ticket\/show\/mW4OMlxv805P2L9j","icon":"ticket","title":"New IT Ticket Arrived","message":"A New IT Ticket Has Been Arised By Max"}";s:7:"read_at";N;s:10:"created_at";s:19:"2025-05-08 04:11:55";s:10:"updated_at";s:19:"2025-05-08 04:11:55";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:4:"data";s:5:"array";s:7:"read_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}s:28:" * escapeWhenCastingToString";b:0;}