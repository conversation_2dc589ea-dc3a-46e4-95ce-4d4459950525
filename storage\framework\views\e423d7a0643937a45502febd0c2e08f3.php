<!-- Employee Info Update Modal -->
<div class="modal fade" data-bs-backdrop="static" id="employeeInfoUpdateModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered">
        <div class="modal-content p-3 p-md-5">
            <button type="button" class="btn-close btn-pinned" data-bs-dismiss="modal" aria-label="Close"></button>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <h3 class="role-title mb-2">Update Your Information</h3>
                    <p class="text-muted">Please update your information for emergency purposes.</p>
                </div>
                <!-- Employee Info Update form -->
                <form method="post" action="<?php echo e(route('administration.my.profile.update.information')); ?>" class="row g-3" autocomplete="off">
                    <?php echo csrf_field(); ?>
                    <?php if(is_invalid_employee_value($user->employee->blood_group)): ?>
                        <div class="mb-3 col-md-12">
                            <label for="blood_group" class="form-label">Blood Group <strong class="text-danger">*</strong></label>
                            <select name="blood_group" id="blood_group" class="form-select select2 w-100 <?php $__errorArgs = ['blood_group'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-style="btn-default" required>
                                <option value="">Select Blood Group</option>
                                <?php $__currentLoopData = $groupedBloodGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $groupLabel => $groupOptions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <optgroup label="<?php echo e($groupLabel); ?>">
                                        <?php $__currentLoopData = $groupOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bloodOption): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($bloodOption->value); ?>">
                                                <?php echo e($bloodOption->value); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </optgroup>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <option value="Unknown">Don't Know (Unknown)</option>
                            </select>
                            <?php $__errorArgs = ['blood_group'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    <?php endif; ?>

                    <?php if(is_invalid_employee_value($user->employee->father_name)): ?>
                        <div class="mb-3 col-md-12">
                            <label for="father_name" class="form-label"><?php echo e(__('Father Name')); ?> <strong class="text-danger">*</strong></label>
                            <input type="text" id="father_name" name="father_name" value="<?php echo e(old('father_name')); ?>" placeholder="<?php echo e(__('Father Name')); ?>" class="form-control <?php $__errorArgs = ['father_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required/>
                            <?php $__errorArgs = ['father_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    <?php endif; ?>

                    <?php if(is_invalid_employee_value($user->employee->mother_name)): ?>
                        <div class="mb-3 col-md-12">
                            <label for="mother_name" class="form-label"><?php echo e(__('Mother Name')); ?> <strong class="text-danger">*</strong></label>
                            <input type="text" id="mother_name" name="mother_name" value="<?php echo e(old('mother_name')); ?>" placeholder="<?php echo e(__('Mother Name')); ?>" class="form-control <?php $__errorArgs = ['mother_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required/>
                            <?php $__errorArgs = ['mother_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    <?php endif; ?>

                    <?php if(is_invalid_employee_value($user->employee->institute_id)): ?>
                        <div class="mb-3 col-md-12">
                            <label for="institute_id" class="form-label"><?php echo e(__('Last / Current Educational Institute')); ?> <strong class="text-danger">*</strong></label>
                            <select name="institute_id" id="institute_id" class="form-select select2-tags <?php $__errorArgs = ['institute_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true" data-tags="true" data-placeholder="Select or type to add new institute" required>
                                <option value=""><?php echo e(__('Select Institute')); ?></option>
                                <?php if(isset($institutes)): ?>
                                    <?php $__currentLoopData = $institutes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $institute): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($institute->id); ?>" <?php echo e(old('institute_id', $user->employee->institute_id) == $institute->id ? 'selected' : ''); ?>>
                                            <?php echo e($institute->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </select>
                            <small class="text-muted">You can type to add a new institute if not found in the list</small>
                            <?php $__errorArgs = ['institute_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    <?php endif; ?>

                    <?php if(is_invalid_employee_value($user->employee->education_level_id)): ?>
                        <div class="mb-3 col-md-12">
                            <label for="education_level_id" class="form-label"><?php echo e(__('Education Level')); ?> <strong class="text-danger">*</strong></label>
                            <select name="education_level_id" id="education_level_id" class="form-select select2-tags <?php $__errorArgs = ['education_level_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" data-allow-clear="true" data-tags="true" data-placeholder="Select or type to add new education level" required>
                                <option value=""><?php echo e(__('Select Education Level')); ?></option>
                                <?php if(isset($educationLevels)): ?>
                                    <?php $__currentLoopData = $educationLevels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $level): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($level->id); ?>" <?php echo e(old('education_level_id', $user->employee->education_level_id) == $level->id ? 'selected' : ''); ?>>
                                            <?php echo e($level->title); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </select>
                            <small class="text-muted">You can type to add a new education level if not found in the list</small>
                            <?php $__errorArgs = ['education_level_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    <?php endif; ?>

                    <?php if(is_invalid_employee_value($user->employee->passing_year)): ?>
                        <div class="mb-3 col-md-12">
                            <label for="passing_year" class="form-label"><?php echo e(__('Passing Year / Exp. Year')); ?> <strong class="text-danger">*</strong></label>
                            <input type="number" id="passing_year" name="passing_year" value="<?php echo e(old('passing_year', $user->employee->passing_year)); ?>" placeholder="<?php echo e(__('e.g., 2020')); ?>" min="1950" max="<?php echo e(date('Y') + 10); ?>" class="form-control <?php $__errorArgs = ['passing_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required/>
                            <?php $__errorArgs = ['passing_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="feather icon-info mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    <?php endif; ?>

                    <div class="col-12 text-center">
                        <button type="submit" class="btn btn-primary me-sm-3 me-1">Save Changes</button>
                        <button type="reset" class="btn btn-label-secondary" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/dashboard/modals/employee_info_update_modal.blade.php ENDPATH**/ ?>