<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rating AJAX Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg">
    <style>
        .ti {
            width: 1em;
            height: 1em;
            display: inline-block;
        }
        .ti-star-filled::before {
            content: "★";
        }
        .ti-star::before {
            content: "☆";
        }
        .ti-check::before {
            content: "✓";
        }
        .ti-loader-2::before {
            content: "⟳";
        }
        .ti-spin {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h2>Daily Work Update Rating Test - Updated Version</h2>
        <p>This demonstrates the AJAX rating functionality with toast notifications and updated button styling.</p>

        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Sample Daily Work Update</h5>
                <p class="card-text">Work update content goes here...</p>

                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span class="badge bg-danger rating-display">Not Reviewed</span>
                    </div>

                    <div class="dropdown">
                        <button class="btn btn-sm btn-dark dropdown-toggle" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="text-white ti ti-check"></i>
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                            <li>
                                <a class="dropdown-item rating-update" href="#" data-rating="1">
                                    <i class="ti ti-star me-2"></i>1
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item rating-update" href="#" data-rating="2">
                                    <i class="ti ti-star me-2"></i>2
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item rating-update" href="#" data-rating="3">
                                    <i class="ti ti-star me-2"></i>3
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item rating-update" href="#" data-rating="4">
                                    <i class="ti ti-star me-2"></i>4
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item rating-update" href="#" data-rating="5">
                                    <i class="ti ti-star me-2"></i>5
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-3">
            <h6>Instructions:</h6>
            <ul>
                <li>Click the dropdown button to see rating options</li>
                <li>Select a rating (1-5) to see the instant update</li>
                <li>The button will show a loading spinner during the request</li>
                <li>Initially: Dark button with checkmark (unrated)</li>
                <li>After rating: Outline-dark button with star and rating number</li>
                <li>The badge will also update to reflect the new rating with appropriate colors</li>
                <li>Toast notification will appear on successful rating</li>
            </ul>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            $('.rating-update').click(function(e) {
                e.preventDefault();

                var $this = $(this);
                var rating = $this.data('rating');
                var $dropdown = $this.closest('.dropdown');
                var $button = $dropdown.find('.dropdown-toggle');

                // Show loading state
                var originalButtonHtml = $button.html();
                $button.html('<i class="text-white ti ti-loader-2 ti-spin"></i>').prop('disabled', true);

                // Close dropdown
                $dropdown.find('.dropdown-menu').removeClass('show');
                $button.removeClass('show').attr('aria-expanded', 'false');

                // Simulate AJAX request with timeout
                setTimeout(function() {
                    // Update button to show rating with new styling
                    $button.html('<i class="ti ti-star-filled me-1"></i>' + rating)
                           .removeClass('btn-dark btn-primary')
                           .addClass('btn-outline-dark')
                           .prop('disabled', false);

                    // Update the rating display
                    var $ratingCell = $this.closest('.card').find('.rating-display');
                    if ($ratingCell.length) {
                        // Determine badge color based on rating
                        var badgeColor = 'success';
                        switch(rating) {
                            case '1': badgeColor = 'danger'; break;
                            case '2': badgeColor = 'warning'; break;
                            case '3': badgeColor = 'dark'; break;
                            case '4': badgeColor = 'primary'; break;
                            default: badgeColor = 'success'; break;
                        }
                        $ratingCell.removeClass('bg-danger bg-warning bg-dark bg-primary bg-success')
                                  .addClass('bg-' + badgeColor)
                                  .text(rating + ' out of 5');
                    }

                    // Show SweetAlert2 toast notification
                    Swal.fire({
                        icon: 'success',
                        title: 'Rating Updated!',
                        text: 'Daily Work Update has been rated ' + rating + ' out of 5 successfully.',
                        timer: 3000,
                        showConfirmButton: false,
                        toast: true,
                        position: 'top-end',
                        timerProgressBar: true
                    });
                }, 1000); // Simulate 1 second delay
            });
        });
    </script>
</body>
</html>
